"use client";
import React, { useEffect } from "react";
import Heading from "./common/Heading";
import Description from "./common/Description";
import CustomButton from "./common/CustomButton";
import { SECURE_DATA_LIST, UPLOAD_DATA_LIST } from "../../utils/helper";
import Icons from "./common/Icons";
import Image from "next/image";
import compareEllips from "../../public/assets/images/webp/compare-left-img.webp";
import AOS from "aos";
import "aos/dist/aos.css";

const GetStarted = () => {
  useEffect(() => {
    AOS.init({
      easing: "ease-in-out",
      once: true,
      offset: 100,
    });
  }, []);
  return (
    <div className="max-xl:px-4 relative overflow-x-clip">
      {/* Left side background image */}
      <Image
        width={379}
        height={379}
        className="absolute left-0 pointer-events-none top-[20%] max-lg:hidden z-[0] opacity-60"
        src={compareEllips}
        alt="background decoration"
        quality={95}
      />
      {/* Right side background image */}
      <Image
        width={379}
        height={379}
        className="absolute right-0 pointer-events-none top-[60%] max-lg:hidden z-[0] opacity-60 transform scale-x-[-1]"
        src={compareEllips}
        alt="background decoration"
        quality={95}
      />
      <div className="max-w-[1170px] mx-auto w-full get-started relative rounded-2xl bg-pink-100 px-4 py-6 lg:px-12 lg:py-10.5">
        <div className="flex gap-6 lg:justify-between max-lg:flex-wrap lg:items-center">
          <div className="max-w-[528px] w-full">
            <div data-aos="fade-up" data-aos-duration={800}>
              {" "}
              <Heading className="!text-left max-md:!mb-3">
                Ready to Get Started?
              </Heading>
            </div>
            <div data-aos="fade-up" data-aos-duration={1000}>
              <Description className="max-sm:!leading-130">
                Join thousands of creators who trust StreamBliss
              </Description>
            </div>
            <div data-aos="fade-up" data-aos-duration={1200}>
              <a href="/register">
                <CustomButton
                  variant="gradiant"
                  className="mt-4 lg:mt-10 lg:!px-[45px] hover:scale-105 hover:shadow-lg hover:shadow-purple-500/25 transition-all duration-300"
                >
                  Start Uploading
                </CustomButton>
              </a>
            </div>
          </div>
          <div className="max-w-[558px] w-full">
            <div className="space-y-3 lg:space-y-[28px]">
              {UPLOAD_DATA_LIST.map((obj, i) => (
                <div
                  data-aos="fade-up"
                  data-aos-duration={i * 100 + 800}
                  key={i}
                  className="w-full p-3 lg:p-[18px] border border-white/15 bg-grey rounded-2xl group hover:shadow-[0px_4px_62.1px_0px_#E649A21F] duration-300 ease-linear transition-all hover:scale-105 hover:shadow-lg hover:shadow-purple-500/20 backdrop-blur-xl"
                >
                  <div className="flex gap-4">
                    <div className="min-w-[42px] h-[42px] md:min-w-[50px] md:h-[50px] flex justify-center items-center upload-box rounded-lg relative group-hover:shadow-[0px_0px_17.23px_0px_#B851E066] duration-300 bg-[linear-gradient(321.29deg,_#FFFFFF33_22.72%,_#FFFFFF00_74.04%)]">
                      <Icons icon={obj.icon} />
                    </div>
                    <div className="mt-0">
                      <p className="text-white font-semibold text-base  md:text-lg leading-140">
                        {obj.heading}
                      </p>
                      <p className="text-white/70 font-normal text-sm md:text-base leading-160 md:mt-1.5">
                        {obj.description}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
        <div
          data-aos="fade-up"
          data-aos-duration={800}
          className="max-w-[1109px] w-full p-3 md:p-5 lg:p-6 rounded-xl bg-custom-gray flex justify-between mt-6 lg:mt-10 max-xl:flex-wrap gap-5"
        >
          {SECURE_DATA_LIST.map((obj, i) => (
            <div className="flex gap-4" key={i}>
              <div className="min-w-[42px] h-[42px] md:min-w-[50px] md:h-[50px] flex justify-center items-center upload-box rounded-lg relative bg-[linear-gradient(321.29deg,_rgba(184,81,224,0.2)_22.72%,_rgba(184,81,224,0)_74.04%)]">
                <Icons icon={obj.icon} />
              </div>
              <div className="mt-0">
                <p className="text-white font-semibold text-md md:text-xl leading-130">
                  {obj.heading}
                </p>
                <p className="font-normal text-sm md:text-base leading-160 text-white opacity-70 md:mt-1.5">
                  {obj.description}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
      <Ellipse position="top-[-37%] -right-[19%]" />
    </div>
  );
};

export default GetStarted;