"use client";
import Heading from "./common/Heading";
import Description from "./common/Description";
import { ACTION_DATA_LIST } from "../../utils/helper";
import Icons from "./common/Icons";
import Image from "next/image";
import action from "../../public/assets/images/svg/action.svg";
import compareEllips from "../../public/assets/images/webp/compare-left-img.webp";
import AOS from "aos";
import "aos/dist/aos.css";
import { useEffect, useState, useRef } from "react";

const Action = () => {
  const [isVisible, setIsVisible] = useState(false);
  const sectionRef = useRef(null);

  useEffect(() => {
    AOS.init({
      easing: "ease-in-out",
      once: true,
      offset: 100,
    });

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.2 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);
  return (
    <div ref={sectionRef} id="how-it-works" className="relative">
      <Image
        width={379}
        height={379}
        className="absolute left-[-10%] pointer-event-none max-lg:hidden -top-146 max-md:-top-20 z-[0] opacity-30"
        src={compareEllips}
        alt="pricing img"
        quality={95}
      />
      <Image
        width={379}
        height={379}
        className="absolute right-[-10%] pointer-event-none max-lg:hidden rotate-180 top-[-60%] max-md:-top-20 z-[0] opacity-30"
        src={compareEllips}
        alt="pricing img"
        quality={95}
      />
      <div className="max-w-[1080px] mx-auto max-xl:px-4">
        <div data-aos="fade-up" data-aos-duration={800}>
          {" "}
          <Heading>StreamBliss in Action</Heading>
        </div>
        <div data-aos="fade-up" data-aos-duration={1000}>
          {" "}
          <Description className="text-center">
            See how easy it is to upload and share your videos.
          </Description>
        </div>
        <div
          data-aos="fade-up"
          data-aos-duration={1000}
          className="w-full bg-gradient-to-r from-black via-white to-black mt-5 max-w-[627px] mx-auto h-[1px] mb-6 md:mb-10.5"
        ></div>
        <div className="flex flex-wrap flex-row items-start max-lg:justify-center justify-between">
          <div className="lg:w-[47%] xl:w-1/2 w-full mb-5 md:mb-0 flex items-center max-lg:justify-center flex-col">
            {ACTION_DATA_LIST.map((obj, i) => (
              <div
                key={i}
                className={`framer-card w-full mb-4 relative z-[2] md:mb-6 max-w-[558px] group last:mb-0 p-3.5 md:px-4 md:py-4.5 border border-white/15 bg-grey rounded-2xl backdrop-blur-xl transition-all duration-700 ease-out ${
                  isVisible
                    ? 'opacity-100 translate-y-0'
                    : 'opacity-0 translate-y-8'
                }`}
                style={{
                  transitionDelay: `${i * 150}ms`,
                }}
              >
                <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-purple-500/0 to-blue-500/0 group-hover:from-purple-500/5 group-hover:to-blue-500/5 transition-all duration-500 ease-out" />
                <div className="absolute inset-0 rounded-2xl border border-transparent group-hover:border-purple-500/20 transition-all duration-500 ease-out" />

                <div className="flex gap-4 relative z-10">
                  <div className="max-md:min-w-10 max-md:min-h-10 max-md:max-h-10 md:min-w-[50px] md:h-[50px] flex justify-center items-center upload-box duration-500 group-hover:shadow-[0px_0px_17.23px_0px_#B851E066] rounded-lg relative bg-[linear-gradient(321.29deg,_rgba(184,81,224,0.2)_22.72%,_rgba(184,81,224,0)_74.04%)] group-hover:scale-105 transition-all ease-out">
                    <Icons
                      className="size-5 md:size-[30px] group-hover:text-purple-300 transition-all duration-500 ease-out"
                      icon={obj.icon}
                    />
                  </div>
                  <div className="mt-0 flex-1">
                    <p className="text-white font-semibold text-base md:text-lg leading-140 group-hover:text-purple-100 transition-all duration-500 ease-out">
                      {obj.heading}
                    </p>
                    <p className="text-white/70 font-normal text-sm max-lg:max-w-[400px] max-xl:max-w-[340px] leading-160 mt-1 md:mt-1.5 group-hover:text-white/90 transition-all duration-500 ease-out">
                      {obj.description}
                    </p>
                  </div>
                </div>

                <div className="absolute inset-0 rounded-2xl group-hover:scale-[1.02] transition-transform duration-500 ease-out pointer-events-none" />
              </div>
            ))}
          </div>
          <div className="lg:w-1/2 w-full flex max-md:mt-9 max-lg:mt-16 justify-center lg:justify-end lg:ps-10">
            <div className={`framer-image transition-all duration-1000 ease-out ${
              isVisible
                ? 'opacity-100 translate-y-0 scale-100'
                : 'opacity-0 translate-y-12 scale-95'
            }`}
            style={{ transitionDelay: '400ms' }}>
              <Image
                src={action}
                alt="StreamBliss in action"
                className="max-w-[470px] max-h-[478px] w-full pointer-events-none rounded-2xl group-hover:scale-[1.02] transition-transform duration-700 ease-out"
                width={470}
                height={478}
                quality={100}
              />

              {/* Framer-style subtle glow */}
              <div className="absolute inset-0 rounded-2xl bg-gradient-to-t from-purple-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-700 ease-out" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Action;