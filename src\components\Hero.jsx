"use client";
import React, {
  useEffect,
  useRef,
  useState,
  useCallback,
  useMemo,
} from "react";
import Header from "./Header";
import Heading from "./common/Heading";
import Description from "./common/Description";
import CustomButton from "./common/CustomButton";
import Image from "next/image";
import Icons from "./common/Icons";
import hero from "../../public/assets/images/svg/hero.png";
import heroLayer from "../../public/assets/images/webp/hero-layer.webp";
import compareEllips from "../../public/assets/images/webp/compare-left-img.webp";
import AOS from "aos";
import "aos/dist/aos.css";
import { useInView } from "react-intersection-observer";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

const Hero = () => {
  const leftBadgeRef = useRef(null);
  const rightBadgeRef = useRef(null);
  const leftContainerRef = useRef(null);
  const rightContainerRef = useRef(null);
  const { ref: statsRef, inView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  const [counters, setCounters] = useState({
    watching: 0,
    creators: 0,
    uploadsToday: 0,
  });

  const [hasAnimated, setHasAnimated] = useState(false);
  const targetValues = useMemo(
    () => ({
      watching: 461,
      creators: 2000,
      uploadsToday: 2069,
    }),
    [],
  );

  const animateCounter = useCallback(
    (key, target, duration = 2000) => {
      if (typeof window === "undefined") return;

      const startValue = counters[key];
      const startTime = performance.now();
      const updateCounter = (currentTime) => {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        const easeOut = 1 - Math.pow(1 - progress, 3);
        const currentValue = Math.floor(
          startValue + (target - startValue) * easeOut,
        );

        setCounters((prev) => ({
          ...prev,
          [key]: currentValue,
        }));

        if (progress < 1) {
          requestAnimationFrame(updateCounter);
        }
      };

      requestAnimationFrame(updateCounter);
    },
    [counters],
  );

  useEffect(() => {
    if (typeof window !== "undefined") {
      AOS.init({
        easing: "ease-in-out",
        once: true,
        offset: 100,
      });
    }
  }, []);

  useEffect(() => {
    if (inView && !hasAnimated) {
      setHasAnimated(true);

      setTimeout(
        () => animateCounter("watching", targetValues.watching, 1800),
        200,
      );
      setTimeout(
        () => animateCounter("creators", targetValues.creators, 2200),
        600,
      );
      setTimeout(
        () => animateCounter("uploadsToday", targetValues.uploadsToday, 2000),
        1000,
      );
    }
  }, [inView, hasAnimated, animateCounter, targetValues]);

  useEffect(() => {
    if (typeof window !== "undefined") {
      gsap.registerPlugin(ScrollTrigger);
      gsap.set(".animate-hero-img", { scale: 1, rotateX: "28deg" });
      gsap.fromTo(
        ".animate-hero-img",
        { rotateX: "28deg" },
        {
          rotateX: 0,
          duration: 2,
          scrollTrigger: {
            trigger: ".animate-section",
            start: "top 80%",
            end: "bottom 60%",
            scrub: 1,
          },
        },
      );
    }
  }, []);

  const handleBadgeMouseMove = (e, badgeRef, containerRef) => {
    if (!badgeRef.current || !containerRef.current) return;

    const container = containerRef.current;
    const badge = badgeRef.current;

    const containerRect = container.getBoundingClientRect();

    const centerX = containerRect.left + containerRect.width / 3;
    const centerY = containerRect.top + containerRect.height / 2;

    const x = (e.clientX - centerX) * 0.4;
    const y = (e.clientY - centerY) * 2;

    const maxMove = 50;
    const limitedX = Math.max(-maxMove, Math.min(maxMove, x));
    const limitedY = Math.max(-maxMove, Math.min(maxMove, y));

    gsap.to(badge, {
      x: limitedX,
      y: limitedY,
      duration: 0.3,
      ease: "power2.out",
    });
  };
  const handleBadgeMouseLeave = (badgeRef) => {
    if (!badgeRef.current) return;

    gsap.to(badgeRef.current, {
      x: 0,
      y: 0,
      duration: 0.5,
      ease: "power2.out",
    });
  };

  return (
    <div className=" !bg-cover !bg-no-repeat relative h-full bg-hero overflow-x-clip">
      <Image
        width={379}
        height={379}
        className="absolute left-0 pointer-event-none top-[-5%] max-lg:hidden z-[0] opacity-40"
        src={compareEllips}
        alt="pricing img"
      />
      <Header />
      <div className=" mt-12 md:mt-[68px] ">
        <div className="relative max-w-[1144px] mx-auto">
          <div
            ref={leftContainerRef}
            className="absolute max-lg:hidden top-[75%] start-[4%] z-10"
            data-aos="fade-right"
            data-aos-duration={1050}
            onMouseMove={(e) =>
              handleBadgeMouseMove(e, leftBadgeRef, leftContainerRef)
            }
            onMouseLeave={() => handleBadgeMouseLeave(leftBadgeRef)}
          >
            <div
              ref={leftBadgeRef}
              className="flex items-center gap-x-2.5 max-w-[226px] bg-badge w-full border-custom-blur-gray justify-center relative border rounded-full px-[22px] py-2.5 cursor-pointer"
            >
              <Icons icon="storage" className="w-5 h-2.5" />
              <Icons
                icon="badgeArrow"
                className="absolute top-[-40%] end-[-6%] w-5 h-5"
              />
              <p className="text-sm md:text-base font-semibold text-nowrap leading-100 mb-0 text-light-white">
                Unlimited Storage
              </p>
            </div>
          </div>
          <div
            ref={rightContainerRef}
            className="absolute max-lg:hidden md:top-[70%] md:end-[2%] xl:top-[104%] xl:end-[3%] z-10"
            data-aos="fade-left"
            data-aos-duration={1300}
            onMouseMove={(e) =>
              handleBadgeMouseMove(e, rightBadgeRef, rightContainerRef)
            }
            onMouseLeave={() => handleBadgeMouseLeave(rightBadgeRef)}
          >
            <div
              ref={rightBadgeRef}
              className="flex items-center gap-x-2.5 max-w-[264px] bg-badge w-full border-custom-blur-gray justify-center relative border rounded-full px-[22px] py-[9px] cursor-pointer"
            >
              <Icons icon="endToEnd" className=" w-5 h-2.5" />
              <Icons
                icon="badgeArrow"
                className="absolute rotate-[-90deg] top-[-38%] start-[-5%] w-5 h-5"
              />
              <p className="text-sm md:text-base font-semibold text-nowrap leading-100 mb-0 text-light-white">
                End-to-End Encryption
              </p>
            </div>
          </div>
          <div data-aos="zoom-in" data-aos-duration={500}>
            <Heading
              variant="6xl"
              className="max-w-[600px] max-lg:px-4 mx-auto flex items-center !font-bold justify-center text-center mb-4 lg:!text-5xl"
            >
              The Creative Cloud <br /> for Everyone
            </Heading>
          </div>
        </div>
        <div data-aos="zoom-in" data-aos-duration={800}>
          <Description className="max-md:max-w-[450px] max-lg:px-4 max-w-[560px] text-center mx-auto mb-6 md:mb-10">
            Store, share, and collaborate on videos, images, docs, and more —
            all in one secure, lightning-fast platform.
          </Description>
        </div>
        <div
          data-aos="fade-up"
          data-aos-duration={800}
          className="flex max-sm:flex-col items-center mx-auto justify-center gap-4 md:gap-6"
        >
          <a href="/register">
            <CustomButton
              variant="gradiant"
              className=" duration-500 xl:!py-[13px] !border !border-transparent md:!px-10 lg:!px-[55px] max-sm:!w-[258px]"
            >
              Start For Free
            </CustomButton>
          </a>
          <CustomButton className="max-sm:!w-[258px] duration-500 xl:!py-[13px] ">
            See Platform in Action
          </CustomButton>
        </div>
        <div className="w-full mx-auto">
          <div className="relative max-xl:px-4 animate-section perspective-distant">
            <Image
              src={hero}
              alt="logo"
              className="pointer-events-none rounded-2xl lg:rounded-[38px] mx-auto flex backdrop-blur-md justify-center items-center max-w-[990px] mt-10 md:mt-[68px] max-h-[630px] w-full animate-hero-img will-change-transform transform-3d hero-cinematic-glow"
              width={771}
              height={630}
              quality={100}
              priority
            />
            <Image
              src={heroLayer}
              alt="logo"
              className="pointer-events-none absolute min-[1630px]:!h-[450px] min-[1930px]:!h-[430px] min-[1900px]:bottom-[-32%] bottom-[-7%] xl:bottom-[-13%]  start-0 end-0 w-full"
              width={771}
              height={630}
              quality={95}
              priority
            />
            <Image
              src={heroLayer}
              alt="logo"
              className="pointer-events-none absolute min-[1930px]:!h-[430px] min-[1900px]:bottom-[-32%] max-xl:bottom-[-12%] bottom-[-29%] z-0 start-0 end-0 w-full"
              width={771}
              height={630}
              quality={95}
              priority
            />
          </div>

          <div
            ref={statsRef}
            className="flex items-center relative z-10 -mt-5 lg:-mt-16 min-[1930px]:mt-10 justify-center gap-4 flex-wrap mb-16 md:mb-20 max-xl:px-4 lg:mb-[115px]"
          >
            <div
              data-aos="fade-up"
              data-aos-duration={600}
              className="flex items-center max-w-[295px] h-12 md:min-h-14 md:max-w-[323px] w-full  bg-custom-black hover:bg-dark-purple duration-500 hero-gradient-border  before:!h-full gap-2.5 justify-center border p-3.5 before:!rounded-xl !rounded-xl"
            >
              <Icons icon="video" />
              <p className="text-white font-medium leading-160 text-sm md:text-base">
                {counters.watching} watching
              </p>
            </div>
            <div
              data-aos="fade-up"
              data-aos-duration={600}
              className="flex items-center max-w-[295px] h-12 md:min-h-14 md:max-w-[323px] w-full  bg-custom-black hover:bg-dark-purple duration-500 hero-gradient-border  before:!h-full gap-2.5 justify-center border p-3.5 before:!rounded-xl !rounded-xl"
            >
              <div className="min-size-7">
                {" "}
                <Icons icon="upload" />
              </div>
              <p className="text-white font-medium leading-160 text-nowrap text-sm md:text-base">
                {counters.creators.toLocaleString()}+ Creators uploading
                now{" "}
              </p>
            </div>
            <div
              data-aos="fade-up"
              data-aos-duration={600}
              className="flex items-center max-w-[295px] h-12 md:min-h-14 md:max-w-[323px] w-full  bg-custom-black hover:bg-dark-purple duration-500 hero-gradient-border  before:!h-full gap-2.5 justify-center border p-3.5 before:!rounded-xl !rounded-xl"
            >
              <Icons icon="today" />
              <p className="text-white font-medium leading-160 text-sm md:text-base">
                {counters.uploadsToday.toLocaleString()} uploads today
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Hero;