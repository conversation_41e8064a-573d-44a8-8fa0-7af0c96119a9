import Image from "next/image";
import React from "react";
import Description from "./common/Description";
import { FOOTER_DATA_LIST } from "../../utils/helper";
import Link from "next/link";
import compareEllips from "../../public/assets/images/webp/compare-left-img.webp";

const Footer = () => {
  const currentYear = new Date().getFullYear();
  return (
    <div className="bg-dark-purple border-t border-solid border-gradient relative overflow-x-clip">
      {/* Left side background image */}
      <Image
        width={379}
        height={379}
        className="absolute left-0 pointer-events-none top-[10%] max-lg:hidden z-[0] opacity-40"
        src={compareEllips}
        alt="background decoration"
        quality={95}
      />
      {/* Right side background image */}
      <Image
        width={379}
        height={379}
        className="absolute right-0 pointer-events-none top-[50%] max-lg:hidden z-[0] opacity-40 transform scale-x-[-1]"
        src={compareEllips}
        alt="background decoration"
        quality={95}
      />
      <div className="max-w-[1080px] container max-xl:px-5 max-sm:px-4 mx-auto lg:pt-20 md:pt-14 pt-10 lg:pb-16 md:pb-12 pb-8">
        <div className="flex lg:flex-row flex-col justify-between gap-7">
          <div className="lg:w-[35%]">
            <Link href="/">
              <Image
                className="xl:max-w-[368px] lg:max-w-[300px] max-w-[230px] xl:h-[73px] lg:h-[60px] h-11 object-cover"
                src="/assets/images/svg/footer-logo.svg"
                alt="Streambliss Logo"
                width={368}
                height={73}
              />
            </Link>
            <Description
              variant="base"
              className="lg:max-w-[368px] max-w-[500px] sm:mt-7 mt-5"
            >
              {" "}
              StreamBliss is a secure, high-speed cloud platform designed for
              creators to store, share, and collaborate on videos, images, and
              documents.
            </Description>
          </div>
          <div className="lg:w-[60%] flex flex-wrap xl:gap-[109px] sm:gap-[63px] gap-10  lg:justify-end justify-between">
            {FOOTER_DATA_LIST.map((obj, i) => (
              <div key={i}>
                <h4 className="font-bold text-lg leading-160 text-white sm:mb-4 mb-2">
                  {obj.title}
                </h4>
                <ul className="sm:space-y-2 space-y-1">
                  {obj.links.map((object, index) => (
                    <li key={index} className="leading-160">
                      <Link
                        href={object.url}
                        className="text-white/70 sm:text-base text-sm font-normal leading-160 hover:text-custom-pink after:transition-all ease-linear duration-300 relative after:absolute after:w-0 after:h-[1px] after:bg-custom-pink after:left-0 after:bottom-0 hover:after:w-full"
                      >
                        {object.label}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </div>
      <Description
        variant="base"
        className="md:py-7 py-5 border-t border-solid border-gradient text-center"
      >
        © {currentYear} StreamBliss. All rights reserved.
      </Description>
    </div>
  );
};

export default Footer;
