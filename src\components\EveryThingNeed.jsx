"use client";
import React, { useEffect, useLayoutEffect } from "react";
import Heading from "./common/Heading";
import Description from "./common/Description";
import Icons from "./common/Icons";
import { FEATURES_LIST } from "../../utils/helper";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import EveryThingNeedMobile from "./EveryThingNeedMobile";
import Image from "next/image";
import compareEllips from "../../public/assets/images/webp/compare-left-img.webp";
import AOS from "aos";
import "aos/dist/aos.css";

const EveryThingNeed = () => {
  useLayoutEffect(() => {
    gsap.registerPlugin(ScrollTrigger);

    const initAnimations = () => {
      const ctx = gsap.context(() => {
        gsap.matchMedia().add("(min-width: 640px)", () => {
          const createScrollAnimation = (endX) => {
            const tl = gsap.timeline();
            ScrollTrigger.create({
              trigger: ".cards_parent",
              start: "top top",
              end: "+=2000",
              scrub: true,
              pin: true,
              pinSpacing: true,
              animation: tl,
            });
            tl.fromTo(".cards_scroll", { x: "10%" }, { x: endX });
          };

          const mm = gsap.matchMedia();
          mm.add("(min-width:1400px)", () => createScrollAnimation("-130%"));
          mm.add("(min-width:1025px) and (max-width:1399px)", () =>
            createScrollAnimation("-160%")
          );
          mm.add("(min-width:992px) and (max-width:1024px)", () =>
            createScrollAnimation("-230%")
          );
          mm.add("(min-width:640px) and (max-width:991px)", () =>
            createScrollAnimation("-330%")
          );
        });
      });

      return ctx;
    };

    let ctx = initAnimations();

    const handleResize = () => {
      ctx.revert();
      ScrollTrigger.getAll().forEach((trigger) => trigger.kill());
      ctx = initAnimations();
    };

    window.addEventListener("resize", handleResize);

    return () => {
      ctx.revert();
      ScrollTrigger.getAll().forEach((trigger) => trigger.kill());
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  useEffect(() => {
    AOS.init({
      easing: "ease-in-out",
      once: true,
      offset: 100,
    });
  }, []);

  return (
    <div
      id="features"
      className="text-white cards_parent sm:overflow-hidden min-h-screen flex justify-center flex-col sm:pb-10 md:pb-0 px-4 sm:px-0 relative z-10 pt-10 sm:pt-0 overflow-x-clip"
    >
      {/* Right side background image */}
      <Image
        width={379}
        height={379}
        className="absolute right-[-10%] pointer-events-none top-[20%] max-lg:hidden z-[0] opacity-30 transform scale-x-[-1]"
        src={compareEllips}
        alt="background decoration"
        quality={95}
      />
      <div className="max-w-[709px] mx-auto">
        <div data-aos="fade-up" data-aos-duration={800}>
          <Heading>Everything You Need</Heading>
        </div>
        <div data-aos="fade-up" data-aos-duration={1000}>
          <Description className="text-center">
            Simple yet powerful features for all your video and image needs
          </Description>
        </div>
        <div
          data-aos="fade-up"
          data-aos-duration={1000}
          className="w-full bg-gradient-to-r from-black via-white to-black mt-5 h-[1px] mb-10.5"
        />
      </div>

      <div className="sm:flex pt-14 cards_scroll hidden">
        {FEATURES_LIST.map((feature, idx) => (
          <div
            key={idx}
            className={`${
              idx % 2 === 0
                ? "bg-[#1F0C1D] -rotate-[15deg]"
                : "bg-[#110018] rotate-15"
            } p-6 rounded-2xl shadow-lg transform hover:-translate-y-2 transition-all duration-300 min-w-[366px] min-h-[305px] flex justify-between flex-col border border-[#3e2e3d]`}
          >
            <div>
              <div className="min-w-[60px] h-[60px] inline-grid justify-center items-center upload-box rounded-lg relative group-hover:shadow-[0px_0px_17.23px_0px_#B851E066] duration-300 bg-[linear-gradient(321.29deg,_#FFFFFF33_22.72%,_#FFFFFF00_74.04%)]">
                <Icons icon={feature.icon} />
              </div>
              <h3 className="font-semibold mb-2 text-xl md:text-[32px] leading-130 pt-4">
                {feature.title}
              </h3>
            </div>
            <p className="text-white opacity-80 text-base">
              {feature.description}
            </p>
          </div>
        ))}
      </div>
      <div className="sm:hidden">
        <EveryThingNeedMobile />
      </div>
    </div>
  );
};

export default EveryThingNeed;
