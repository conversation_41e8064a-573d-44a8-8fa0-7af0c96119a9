"use client";
import React, { useEffect, useRef, useState } from "react";
import Icons from "./common/Icons";
import Description from "./common/Description";
import { ACCORDION_DATA_LIST } from "../../utils/helper";
import Heading from "./common/Heading";
import Image from "next/image";
import compareEllips from "../../public/assets/images/webp/compare-left-img.webp";
import AOS from "aos";
import "aos/dist/aos.css";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

const Faq = () => {
  const [activeIndex, setActiveIndex] = useState(0);
  const contentRefs = useRef([]);
  const [heights, setHeights] = useState({});

  const toggleAccordion = (index) => {
    setActiveIndex((prevIndex) => (prevIndex === index ? null : index));
  };

  useEffect(() => {
    const newHeights = {};
    contentRefs.current.forEach((el, i) => {
      if (el) {
        newHeights[i] = el.scrollHeight;
      }
    });
    setHeights(newHeights);
  }, []);
  useEffect(() => {
    const handleResize = () => {
      const updatedHeights = {};
      contentRefs.current.forEach((el, i) => {
        if (el) {
          updatedHeights[i] = el.scrollHeight;
        }
      });
      setHeights(updatedHeights);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  useEffect(() => {
    gsap.registerPlugin(ScrollTrigger);

    const items = gsap.utils.toArray(".accordion-item");

    items.forEach((item, index) => {
      gsap.fromTo(
        item,
        { y: 100, opacity: 0 },
        {
          y: 0,
          opacity: 1,
          duration: 0.5 + index * 0.2, // duration increases with each item
          scrollTrigger: {
            trigger: item,
            start: "top 60%",
            end: "bottom center",
            toggleActions: "play none none none",
          },
        }
      );
    });
  }, []);

  useEffect(() => {
    AOS.init({
      easing: "ease-in-out",
      once: true,
      offset: 100,
    });
  }, []);
  return (
    <div className="py-10 md:py-16 lg:py-[140px] relative overflow-x-clip">
      {/* Right side background image */}
      <Image
        width={379}
        height={379}
        className="absolute right-0 pointer-events-none top-[20%] max-lg:hidden z-[0] opacity-40 transform scale-x-[-1]"
        src={compareEllips}
        alt="background decoration"
        quality={95}
      />

      <div className="max-w-[1180px] container sm:px-5 px-4 mx-auto ">
        <div data-aos="fade-up" data-aos-duration={800}>
          <Heading>Frequently Asked Questions</Heading>
        </div>
        <div data-aos="fade-up" data-aos-duration={1000}>
          <Description className="text-center">
            Choose the plan that works best for you
          </Description>
        </div>
        <div
          data-aos="fade-up"
          data-aos-duration={1000}
          className="max-w-[694px] h-[1px] border-t border-solid border-gradient mt-5 mx-auto lg:mb-6.5 mb-3.5"
        ></div>

        <div className="lg:max-w-[816px] max-w-[616px] mx-auto">
          {ACCORDION_DATA_LIST.map((faq, index) => {
            const isOpen = activeIndex === index;
            return (
              <div
                key={index}
                className={`accordion-item border-b border-solid border-white/20 transition-all ease-linear duration-300 hover:bg-white/5 rounded-lg px-4 backdrop-blur-xl ${
                  isOpen ? "lg:pb-10 sm:pb-6 pb-5 bg-white/5" : ""
                }`}
              >
                <div
                  onClick={() => toggleAccordion(index)}
                  className={`flex justify-between items-start lg:pt-6 sm:pt-5 pt-4 cursor-pointer transition-all ease-linear duration-300 ${
                    isOpen ? "lg:pb-4 sm:pb-3 pb-2" : "lg:pb-6 sm:pb-5 pb-4"
                  }`}
                >
                  <span className="font-semibold lg:text-2xl sm:text-xl text-base leading-160 text-white">
                    {faq.question}
                  </span>
                  <span className="ms-1 max-sm:mt-1">
                    <Icons
                      className=""
                      icon={isOpen ? "minusIcon" : "plusIcon"}
                    />
                  </span>
                </div>
                <div
                  ref={(el) => (contentRefs.current[index] = el)}
                  style={{
                    maxHeight: isOpen ? `${heights[index] || 0}px` : "0px",
                    overflow: "hidden",
                    transition: "max-height 0.5s ease-in-out",
                  }}
                >
                  <p className="font-normal sm:text-base text-sm leading-160 text-white/70 lg:max-w-[698px] max-w-[540px]">
                    {faq.answer}
                  </p>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default Faq;