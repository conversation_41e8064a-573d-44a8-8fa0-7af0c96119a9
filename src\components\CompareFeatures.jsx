"use client";
import React from "react";
import { COMPARE_FEATURE_LIST } from "../../utils/helper";
import Image from "next/image";
import compareEllips from "../../public/assets/images/webp/compare-left-img.webp";
import AOS from "aos";
import "aos/dist/aos.css";
import { useEffect } from "react";

const CompareFeatures = () => {
  useEffect(() => {
    AOS.init({
      easing: "ease-in-out",
      once: true,
      offset: 100,
    });
  }, []);
  return (
    <div className="relative">
      <Image
        width={379}
        height={379}
        className="absolute left-0 -top-146 max-md:-top-20 -z-[1] opacity-40"
        src={compareEllips}
        alt="pricing img"
        quality={95}
      />
      <div className="w-full max-w-[1080px] max-[1143.98px]:px-4 mx-auto">
        <h3
          data-aos="fade-up"
          data-aos-duration={800}
          className="text-2xl font-semibold pb-6 text-white leading-160 max-md:pb-3 max-md:text-xl"
        >
          Compare Features
        </h3>
        <div data-aos="fade-up" data-aos-duration={1000}>
          <div className="overflow-auto rounded-2xl max-md:rounded-xl border border-[#282529] bg-[#070108BD] table-scollbar">
            <table className="w-full text-left border-collapse bg-[#070108BD]">
              <thead>
                <tr>
                  {COMPARE_FEATURE_LIST.map((col, index) => (
                    <th
                      key={index}
                      className={`p-4 font-semibold text-white bg-[#140016] text-xl leading-130 max-lg:text-lg max-md:text-base ${
                        index === 0 ? "text-start pl-7" : "text-center"
                      }`}
                    >
                      {col.title}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {COMPARE_FEATURE_LIST[0].list.map((_, rowIndex) => (
                  <tr key={rowIndex} className="border-t border-[#1E1A1F]">
                    {COMPARE_FEATURE_LIST.map((col, colIndex) => (
                      <td
                        key={colIndex}
                        className={`py-3 text-base text-white opacity-70 max-lg:text-sm whitespace-nowrap ${
                          colIndex === 0
                            ? "text-start w-[258px] max-md:min-w-[160px] bg-[#0E030F] pl-7 pr-4"
                            : "text-center px-4 max-w-[194px] max-lg:min-w-[192px] max-md:min-w-[200px]"
                        }`}
                      >
                        {col.list[rowIndex]}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CompareFeatures;
